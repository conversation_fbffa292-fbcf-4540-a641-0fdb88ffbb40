const crypto = require("crypto");

/**
 * Encryption utility for securing sensitive data like Slack bot tokens
 */
class EncryptionUtil {
  constructor() {
    this.algorithm = "aes-256-gcm";
    this.keyLength = 32; // 256 bits
    this.ivLength = 16; // 128 bits
    this.tagLength = 16; // 128 bits

    // Get encryption key from environment variable
    this.encryptionKey = process.env.SLACK_ENCRYPTION_KEY;

    if (!this.encryptionKey) {
      throw new Error("SLACK_ENCRYPTION_KEY environment variable is required");
    }

    if (this.encryptionKey.length !== this.keyLength) {
      throw new Error(
        `SLACK_ENCRYPTION_KEY must be exactly ${this.keyLength} characters long`
      );
    }
  }

  /**
   * Encrypt a string
   * @param {string} text - Text to encrypt
   * @returns {string} Encrypted text in format: iv:tag:encrypted
   */
  encrypt(text) {
    if (!text || typeof text !== "string") {
      throw new Error("Text to encrypt must be a non-empty string");
    }

    try {
      // Generate random IV
      const iv = crypto.randomBytes(this.ivLength);

      // Create cipher
      const cipher = crypto.createCipher(
        this.algorithm,
        this.encryptionKey,
        iv
      );
      cipher.setAAD(Buffer.from("slack-token", "utf8")); // Additional authenticated data

      // Encrypt
      let encrypted = cipher.update(text, "utf8", "hex");
      encrypted += cipher.final("hex");

      // Get authentication tag
      const tag = cipher.getAuthTag();

      // Return format: iv:tag:encrypted (all in hex)
      return `${iv.toString("hex")}:${tag.toString("hex")}:${encrypted}`;
    } catch (error) {
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * Decrypt a string
   * @param {string} encryptedData - Encrypted data in format: iv:tag:encrypted
   * @returns {string} Decrypted text
   */
  decrypt(encryptedData) {
    if (!encryptedData || typeof encryptedData !== "string") {
      throw new Error("Encrypted data must be a non-empty string");
    }

    try {
      // Parse the encrypted data
      const parts = encryptedData.split(":");
      if (parts.length !== 3) {
        throw new Error("Invalid encrypted data format");
      }

      const [ivHex, tagHex, encrypted] = parts;

      // Convert from hex
      const iv = Buffer.from(ivHex, "hex");
      const tag = Buffer.from(tagHex, "hex");

      // Create decipher
      const decipher = crypto.createDecipher(
        this.algorithm,
        this.encryptionKey,
        iv
      );
      decipher.setAAD(Buffer.from("slack-token", "utf8")); // Same AAD as encryption
      decipher.setAuthTag(tag);

      // Decrypt
      let decrypted = decipher.update(encrypted, "hex", "utf8");
      decrypted += decipher.final("utf8");

      return decrypted;
    } catch (error) {
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  /**
   * Generate a random encryption key
   * @returns {string} Random 32-character key
   */
  static generateKey() {
    return crypto.randomBytes(32).toString("hex").substring(0, 32);
  }

  /**
   * Validate if a string is properly encrypted
   * @param {string} encryptedData - Data to validate
   * @returns {boolean} True if valid encrypted format
   */
  isValidEncryptedFormat(encryptedData) {
    if (!encryptedData || typeof encryptedData !== "string") {
      return false;
    }

    const parts = encryptedData.split(":");
    if (parts.length !== 3) {
      return false;
    }

    const [ivHex, tagHex, encrypted] = parts;

    // Check if all parts are valid hex strings
    const hexRegex = /^[0-9a-fA-F]+$/;
    return (
      hexRegex.test(ivHex) &&
      hexRegex.test(tagHex) &&
      hexRegex.test(encrypted) &&
      ivHex.length === this.ivLength * 2 && // IV should be 32 hex chars (16 bytes)
      tagHex.length === this.tagLength * 2
    ); // Tag should be 32 hex chars (16 bytes)
  }
}

// Create singleton instance
let encryptionInstance = null;

/**
 * Get encryption utility instance
 * @returns {EncryptionUtil}
 */
function getEncryptionUtil() {
  if (!encryptionInstance) {
    encryptionInstance = new EncryptionUtil();
  }
  return encryptionInstance;
}

module.exports = {
  EncryptionUtil,
  getEncryptionUtil,
};
